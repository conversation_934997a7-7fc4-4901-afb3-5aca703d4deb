---
title: Get a sales order
description: Get a sales order endpoint documentation.
---


Relationships can be included via the `include` query parameter.

### Request endpoint

```http
GET /{companyId}/sales-orders/{salesOrderId}
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter    | Type          | Required | Description                    |
|--------------|---------------|----------|--------------------------------|
| companyId    | string <uuid> | Yes      | Your inFlow account companyId  |
| salesOrderId | string <uuid> | Yes      | The salesOrderId to be fetched |

### Response

#### Success response (200) schema: `application/json`

| Field                  | Type                | Description      |
|------------------------|---------------------|------------------|
| amountPaid             | string <decimal>    | The amount that this customer has paid you. |
| assignedToTeamMember   | object (TeamMember) |                  |
| assignedToTeamMemberId | string <uuid>       |                  |
| balance                | string <decimal>    | The remaining amount that the customer owes you. |
| billingAddress         | object (Address)    |                  |
| calculateTax2OnTax1    | boolean             | Whether a secondary tax should be compounded on top of the primary tax |
| confirmerTeamMember    | object (TeamMember) |                  |
| confirmerTeamMemberId  | string <uuid>       |                  |
| contactName            | string              | The name of the customer's employee that you should contact for this order |
| costOfGoodsSold        | object (SalesOrderCostOfGoodsSold) |   |
| currency               | object (Currency)   |                  |
| currencyId             | string <uuid>       |                  |
| customFields           | object (LargeCustomFieldValues)|       |
| customer               | object (Customer)   |                  |
| customerId             | string <uuid>       |                  |
| dueDate                | string <date-time>  | The date by which payment is due |
| email                  | string              | The email address for the customer that you should contact for this order |
| exchangeRate           | string <decimal>    | The exchange rate between the currency in this order and your home currency effective for this order |
| exchangeRateAutoPulled | string <date-time>  | If this exchange rate was automatically pulled, then the date it was set, otherwise null. |
| externalId             | string              | An optional external identifier, for use in integrating with other systems |
| inventoryStatus        | string              | The inventory-related status of this order. This is a read-only attribute. The inventoryStatus is calculated based on whether all products have been added to pickLines. For orders with shipping, all products also have to be added to packLines and shipLines to mark the order fulfilled. |
| invoicedDate           | string <date-time>  | The date that you sent an invoice for this customer |
| isCancelled            | boolean             | Whether this order is cancelled (being cancelled voids any payments and inventory movements) |
| isCompleted            | boolean             | Whether this order is completed (fully shipped and returns processed) |
| isInvoiced             | boolean             | Whether you have issued an invoice to this customer for this order. |
| isPrioritized          | boolean             | Whether this order is prioritized for fulfillment. (This is a read-only attribute.) |
| isQuote                | boolean             | When `true`, then treat this as a sales quote (where the customer hasn't agreed to purchase yet) instead of an order. |
| isTaxInclusive         | boolean             | When `true`, then prices should be treated as tax-inclusive. |
| lastModifiedBy         | object (TeamMember) |                  |     
| lastModifiedById       | string <uuid>       | The inFlow Team Member, system process, or API key that last modified this sales order. This is set automatically, and cannot be set through the API. |
| lines                  | Array of objects    | Lines representing which goods have been ordered and returned |
| location               | object (Location)   |                  |
| locationId             | string <uuid>       |                  |
| needsConfirmation      | boolean             | When the following conditions are met, then this order needs confirmation before it should be fulfilled: needsConfirmation = True; confirmerTeamMemberId = Null; isQuote = False |
| nonCustomerCost        | object (PercentOrFixedAmount) |        |
| orderDate              | string <date-time>  | The date this order was placed. |
| orderFreight           | string <decimal>    | The amount you charge this customer for shipping |
| orderNumber            | string              | An identifier for this sales order and shown on printed documents. |
| orderRemarks           | string              | Any extra comments on this order |
| packLines              | Array of objects    | Lines representing which goods have been packed into which boxes for shipping |
| packRemarks            | string              | Any extra comments on this order regarding packing |
| paymentLines           | Array of objects    | Lines representing a history of payment details for this order. |
| paymentStatus          | string              | The payment-related status of this order |
| paymentTerms           | object (PaymentTerms) |                |
| paymentTermsId         | string <uuid>       |                  |
| phone                  | string              | The phone number for the customer that you should contact for this order |
| pickLines              | Array of objects    | Lines representing which goods have been picked from your warehouse |
| pickRemarks            | string              | Any extra comments on this order regarding picking |
| poNumber               | string              | The customer's Purchase Order number for this order. |
| pricingScheme          | object (PricingScheme) |                |
| pricingSchemeId        | string <uuid>       |                   |
| requestedShipDate      | string <date-time>  | The date that you should ship this order |
| restockLines           | Array of objects    | Lines representing which returned items have been restocked |
| restockRemarks         | string              | Any extra comments on this order regarding restocking |
| returnFee              | string <decimal>    | The amount you charge to this customer for return fees |
| returnFreight          | string <decimal>    | The amount that you refund to this customer for returns related to shipping |
| returnRemarks          | string              | Any extra comments on this order regarding returns |
| salesOrderId           | string <uuid>       | The primary identifier for this sales order. [When inserting, you should specify this by generating a GUID](/overview/#write-requests). Not shown to users |
| salesRep               | string              | The name of the sales rep at your company in charge of this order. Note: this can only be set when legacy free-form sales rep values are allowed. |
| salesRepTeamMember     | object (TeamMember) |                  |
| salesRepTeamMemberId   | string <uuid>       |                  |
| sameBillingAndShipping | boolean             | When `true`, then the shipping address should be the same as the billing address. |
| shipLines              | Array of objects    | Lines representing which boxes have been shipped |
| shipRemarks            | string              | Any extra comments on this order regarding shipping |
| shipToCompanyName      | string              | The ship-to company name shown on printed documents |
| shippingAddress        | object (Address)    |                  |
| showShipping           | boolean             | Whether this order will be shipped; this controls whether certain fields like Shipping Address will be shown. |
| source                 | string              | Where this order originated from, i.e. the name of your app |
| subTotal               | string <decimal>    | The total of line items for this order |
| tax1                   | string <decimal>    | The calculated primary tax amount for this order |
| tax1Name               | string              | A short name for display of the primary tax |
| tax1OnShipping         | boolean             | Whether the primary tax applies to shipping/freight costs |
| tax1Rate               | string <decimal>    | The default percentage primary tax for this order. |
| tax2                   | string <decimal>    | The calculated secondary tax amount for this order |
| tax2Name               | string              | A short name for display of the secondary tax |
| tax2OnShipping         | boolean             | Whether the secondary tax applies to shipping/freight costs |
| tax2Rate               | string <decimal>    | The default percentage secondary tax for this order. |
| taxingScheme           | object (TaxingScheme) |                |
| taxingSchemeId         | string <uuid>       |                  |
| timestamp              | string <rowversion> | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |
| total                  | string <decimal>    | The total amount the customer should pay, including taxes and shipping |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 9221 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-get-a-sales-order.json](response-sample-of-get-a-sales-order.json)
