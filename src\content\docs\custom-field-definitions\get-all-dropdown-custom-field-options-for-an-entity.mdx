---
title: Get all dropdown custom field options for an entity
description: Get all dropdown custom field options for an entity endpoint documentation.
---

### Request endpoint

```http
GET /{companyId}/custom-field-dropdown-options/{entityType}
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter  | Type          | Required | Description                   |
|------------|---------------|----------|-------------------------------|
| companyId  | string <uuid> | yes      | Your inFlow account companyId |
| entityType | string        | yes      | The entity type of the custom field, e.g. `product` |

Available enum string values for `entityType`:
- "SalesOrder"
- "PurchaseOrder"
- "Product"
- "Vendor"
- "Customer"
- "CountSheet"
- "StockTransfer"
- "StockAdjustment"
- "StockCount"
- "ManufacturingOrder"

### Response

#### Success response (200) schema: `application/json`

Array of `CustomFieldDropdownOption` objects. Each `CustomFieldDropdownOption` object has the following properties:

| Field         | Type            | Description |
|---------------|-----------------|-------------|
| attributes    | object Nullable |             |
| relationships | object Nullable |             |
| meta          | object Nullable |             |

#### Success response (200) example

##### Content type: `application/json`

```json
[
  {
    "attributes": {
      "property1": {},
      "property2": {}
    },
    "relationships": {
      "property1": [
        {
          "attributes": {
            "property1": {},
            "property2": {}
          },
          "relationships": {
            "property1": [
              {}
            ],
            "property2": [
              {}
            ]
          },
          "meta": {
            "property1": {},
            "property2": {}
          }
        }
      ],
      "property2": [
        {
          "attributes": {
            "property1": {},
            "property2": {}
          },
          "relationships": {
            "property1": [
              {}
            ],
            "property2": [
              {}
            ]
          },
          "meta": {
            "property1": {},
            "property2": {}
          }
        }
      ]
    },
    "meta": {
      "property1": {},
      "property2": {}
    }
  }
]
```
