---
title: List manufacture orders
description: List manufacture orders endpoint documentation.
---


Relationships can be included via the `include` query parameter.  

Options for filtering this list:
- `filter[manufacturingOrderNumber]`
- `filter[status] array of statuses`
- `filter[isPrioritized]`
- `filter[orderDate]` date range object with `fromDate` and `toDate`
- `filter[dueDate]` date range object with `fromDate` and `toDate`
- `filter[locationId]`
- `filter[assignedTo]`
- `filter[isActive]`

### Request endpoint

```http
GET /{companyId}/manufacturing-orders
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string &lt;uuid&gt; | yes      | Your inFlow account companyId |

### Query parameters

| Parameter            | Type              | Description               |
|----------------------|-------------------|---------------------------|
| request              | object (GetCollectionRequest) | Additional query parameter options |
| request.includeCount | boolean           | Return count in X-listCount header |
| request.count        | integer (int32)   | Max 100 per request       |
| request.after        | string (Nullable) | Entity ID for pagination  |
| request.before       | string (Nullable) | Entity ID for pagination  |
| request.start        | string (Nullable) | Include this entity and everything after |
| request.skip         | integer (int32)   | Number of records to skip |
| request.sort         | string (Nullable) | Property name to sort by  |
| request.sortDesc     | boolean           | If true, sort descending  |

### Response

#### Success response (200) schema: `application/json`

Array of `ManufacturingOrder` objects. Each `ManufacturingOrder` object has the following properties:

| Field                     | Type                     | Description   |
|---------------------------|--------------------------|---------------|
| assignedToTeamMember      | object (TeamMember)      |               |
| assignedToTeamMemberId    | string &lt;uuid&gt; (Nullable) |               |
| completedDate             | string &lt;date-time&gt; (Nullable) | The date this order was completed. |
| currency                  | object (Currency)        |               |
| currencyId                | string &lt;uuid&gt; (Nullable) |               |
| customFields              | object (LargeCustomFieldValues) |        |
| dueDate                   | string &lt;date-time&gt; (Nullable) | The due date this order should be completed by. |
| extraCosts                | string &lt;decimal&gt; (Nullable) | Deprecated - use operation costs instead. Any extra costs for this order, in your home currency. |
| isCancelled               | boolean                  | Whether this order is cancelled (being cancelled voids any inventory movements) |
| isCompleted               | boolean                  | Whether this order is completed (put-away lines do not take effect until completed) |
| isPrioritized             | boolean                  | Whether this order is prioritized for completion. (This is a read-only attribute.) |
| lastModifiedBy            | object (TeamMember)      |               |
| lastModifiedById          | string &lt;uuid&gt;            | The inFlow Team Member, system process, or API key that last modified this manufacture order. This is set automatically, and cannot be set through the API. |
| lines                     | Array of objects         | Lines representing the definition of the finished products and raw materials. To specify child lines within these lines, add a property called `manufacturingOrderLines` within these lines, an array recursively containing other ManufacturingOrderLines. |
| location                  | object (Location)        |               |
| locationId                | string &lt;uuid&gt;            |               |
| manufacturingOrderId      | string &lt;uuid&gt;            | The primary identifier for this manufacture order. [When inserting, you should specify this by generating a GUID](/overview/#write-requests). Not shown to users |
| manufacturingOrderNumber  | string                   | An identifier for this manufacture order and shown on printed documents. |
| orderDate                 | string &lt;date-time&gt;       | The date this manufacture order was created. |
| pickLines                 | Array of objects         | Lines representing which raw materials have been picked from your warehouse |
| pickMatchings             | Array of objects         | Lines representing which pick lines correspond to which order lines |
| pickRemarks               | string                   | Any extra comments on this order regarding picking raw materials |
| primaryFinishedProduct    | object (Product)         |               |
| primaryFinishedProductId  | string &lt;uuid&gt; (Nullable) |               |
| putAwayRemarks            | string                   | Any extra comments on this order regarding putting finished products away |
| putLines                  | Array of objects         | Lines representing which finished products have been put away |
| remarks                   | string                   | Any extra comments on this order |
| splitPartNumber           | integer <int32> (Nullable) | Deprecated - to be deleted |
| status                    | string                   | The status of this order<br>Enum: `"Open"` `"InProgress"` `"Completed"` |
| timestamp                 | string <rowversion>      | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |
| version                   | integer <int32>          | Deprecated - to be deleted |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 12648 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-list-manufacture-orders.json](response-sample-of-list-manufacture-orders.json)
