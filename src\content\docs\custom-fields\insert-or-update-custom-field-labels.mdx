---
title: Insert or Update Custom Field Labels
description: Insert or Update Custom Field Labels endpoint documentation.
---

### Request endpoint

```http
PUT /{companyId}/custom-fields
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| companyId | string <uuid> | required | Your inFlow account companyId |

### Request body

**Request body schema:** `application/json`

`customFieldsId` refers to the companyID, not customfieldsID.

| Field                                 | Type          | Description   |
|---------------------------------------|---------------|---------------|
| customFieldsId                        | string <uuid> | Although the name of the ID is custom fields, this value represents the company ID. |
| purchaseOrderCustomFieldPrintLabels   | `CustomFieldPrintValues` object |  |
| salesOrderCustomFieldPrintLabels      | `CustomFieldPrintValues` object |  |
| stockAdjustmentCustomFieldPrintLabels | `CustomFieldPrintValues` object |  |
| stockTransferCustomFieldPrintLabels   | `CustomFieldPrintValues` object |  |
| workOrderCustomFieldPrintLabels       | `CustomFieldPrintValues` object |  |

### Payload example

```json
{
  "customFieldsId": "********-0000-0000-0000-************",
  "purchaseOrderCustomFieldPrintLabels": {
    "custom1Print": true,
    "custom2Print": true,
    "custom3Print": true
  },
  "salesOrderCustomFieldPrintLabels": {
    "custom1Print": true,
    "custom2Print": true,
    "custom3Print": true
  },
  "stockAdjustmentCustomFieldPrintLabels": {
    "custom1Print": true,
    "custom2Print": true,
    "custom3Print": true
  },
  "stockTransferCustomFieldPrintLabels": {
    "custom1Print": true,
    "custom2Print": true,
    "custom3Print": true
  },
  "workOrderCustomFieldPrintLabels": {
    "custom1Print": true,
    "custom2Print": true,
    "custom3Print": true
  }
}
```

### Response

#### Success response (200) schema: `application/json`

| Field                                 | Type          | Description   |
|---------------------------------------|---------------|---------------|
| customFieldsId                        | string <uuid> | Although the name of the ID is custom fields, this value represents the company ID. |
| purchaseOrderCustomFieldPrintLabels   | `CustomFieldPrintValues` object |  |
| salesOrderCustomFieldPrintLabels      | `CustomFieldPrintValues` object |  |
| stockAdjustmentCustomFieldPrintLabels | `CustomFieldPrintValues` object |  |
| stockTransferCustomFieldPrintLabels   | `CustomFieldPrintValues` object |  |
| workOrderCustomFieldPrintLabels       | `CustomFieldPrintValues` object |  |

#### Success response (200) example

##### Content type: `application/json`

```json
{
  "customFieldsId": "********-0000-0000-0000-************",
  "purchaseOrderCustomFieldPrintLabels": {
    "custom1Print": true,
    "custom2Print": true,
    "custom3Print": true
  },
  "salesOrderCustomFieldPrintLabels": {
    "custom1Print": true,
    "custom2Print": true,
    "custom3Print": true
  },
  "stockAdjustmentCustomFieldPrintLabels": {
    "custom1Print": true,
    "custom2Print": true,
    "custom3Print": true
  },
  "stockTransferCustomFieldPrintLabels": {
    "custom1Print": true,
    "custom2Print": true,
    "custom3Print": true
  },
  "workOrderCustomFieldPrintLabels": {
    "custom1Print": true,
    "custom2Print": true,
    "custom3Print": true
  }
}
```
