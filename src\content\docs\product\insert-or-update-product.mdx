---
title: Insert or update product
description: Insert or update product endpoint documentation.
---


### Request endpoint

```http
PUT /{companyId}/products
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string &lt;uuid&gt; | yes      | Your inFlow account companyId |

### Request body

**Request body schema:** `application/json`

A product to insert or update.

**Note**:
- `productId` property is required, please generate a GUID when inserting.

| Field                     | Type                          | Description |
|---------------------------|-------------------------------|-------------|
| autoAssemble              | boolean                       | Only relevant for products with a bill of materials. Whether this product should be automatically by manufacture order when it's picked from a location where it's not in stock. |
| category                  | object (Category)             |             |
| categoryId                | string &lt;uuid&gt;                 |             |
| cost                      | object (ProductCost)          |             |
| customFields              | object (LargeCustomFieldValues)|            |
| defaultImage              | object (Image)                |             |
| defaultImageId            | string &lt;uuid&gt; Nullable        |             |
| defaultPrice              | object (ProductPrice)         |             |
| description               | string                        | A human-readable description for this product. |
| height                    | string &lt;decimal&gt; Nullable     | Height of this product (unit depends on global setting) |
| hsTariffNumber            | string                        | Harmonized Tariff Schedule code for customs when exporting this item. Used to pre-fill for customs forms when shipping. |
| images                    | Array of objects              |             |
| includeQuantityBuildable  | boolean                       | Only relevant for products with a bill of materials. Whether this product should add the Quantity Buildable when showing quantities. |
| inventoryLines            | Array of objects              |             |
| isActive                  | boolean                       | Products with `IsActive = false` are deactivated and hidden away for future usage. |
| isManufacturable          | boolean                       | Whether this product can be manufactured. This is a read-only property that is calculated based on whether this product has a bill of materials defined. |
| itemBoms                  | Array of objects              |             |
| itemType                  | string                        | The type of this item. Cannot be changed once set. Stocked Product is most common.<br>Enum: "StockedProduct", "NonstockedProduct", "Service" |
| lastModifiedBy            | object (TeamMember)           |             |
| lastModifiedById          | string &lt;uuid&gt;                 | The inFlow Team Member, system process, or API key that last modified this product. This is set automatically, and cannot be set through the API. |
| lastModifiedDateTime      | string &lt;date-time&gt;            | Last modified datetime of this product. |
| lastVendor                | object (Vendor)               |             |
| lastVendorId              | string &lt;uuid&gt; Nullable        |             |
| length                    | string &lt;decimal&gt; Nullable     | Length of this product (unit depends on global setting) |
| name                      | string                        | An item name or title that should be human readable. Must be unique. |
| originCountry             | string                        | 2-character country code for country of origin. Used to pre-fill for customs forms when shipping. |
| prices                    | Array of objects              |             |
| productBarcodes           | Array of objects              |             |
| productId                 | string &lt;uuid&gt;                 | The primary identifier for this product. [When inserting, you should specify this by generating a GUID](/overview/#write-requests). Not shown to users |
| productOperations         | Array of objects              |             |
| purchasingUom             | object (UnitOfMeasure)        |             |
| remarks                   | string                        | A space for any extra remarks about this product. |
| reorderSettings           | Array of objects              |             |
| salesUom                  | object (UnitOfMeasure)        |             |
| sku                       | string                        | A SKU (stock-keeping-unit) code that represents a product in a concise, machine-friendly, way. Optional, but must be unique if specified. |
| standardUomName           | string                        | Standard unit of measure for tracking this product |
| taxCodes                  | Array of objects              |             |
| timestamp                 | string <rowversion>           | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |
| totalQuantityOnHand       | string &lt;decimal&gt;              | The total inventory quantity on hand across all locations for this product. Note that the InventoryLines relationship must be included for this attribute to be populated. |
| trackSerials              | boolean                       | Whether this product requires serial numbers. Cannot be changed once set. Only possible for Stocked Products. |
| vendorItems               | Array of objects              | A list of vendors that sell this item to you |
| weight                    | string &lt;decimal&gt; Nullable     | Weight of this product (unit depends on global setting) |
| width                     | string &lt;decimal&gt; Nullable     | Width of this product (unit depends on global setting) |

### Payload example

> **WARNING**: The payload sample is 1229 lines long.
> Please follow the link below to see payload JSON.

[payload-sample-of-insert-or-update-product.json](payload-sample-of-insert-or-update-product.json)

### Response

#### Success response (200) schema: `application/json`

| Field                     | Type                          | Description |
|---------------------------|-------------------------------|-------------|
| autoAssemble              | boolean                       | Only relevant for products with a bill of materials. Whether this product should be automatically by manufacture order when it's picked from a location where it's not in stock. |
| category                  | object (Category)             |             |
| categoryId                | string &lt;uuid&gt;                 |             |
| cost                      | object (ProductCost)          |             |
| customFields              | object (LargeCustomFieldValues)|            |
| defaultImage              | object (Image)                |             |
| defaultImageId            | string &lt;uuid&gt; Nullable        |             |
| defaultPrice              | object (ProductPrice)         |             |
| description               | string                        | A human-readable description for this product. |
| height                    | string &lt;decimal&gt; Nullable     | Height of this product (unit depends on global setting) |
| hsTariffNumber            | string                        | Harmonized Tariff Schedule code for customs when exporting this item. Used to pre-fill for customs forms when shipping. |
| images                    | Array of objects              |             |
| includeQuantityBuildable  | boolean                       | Only relevant for products with a bill of materials. Whether this product should add the Quantity Buildable when showing quantities. |
| inventoryLines            | Array of objects              |             |
| isActive                  | boolean                       | Products with `IsActive = false` are deactivated and hidden away for future usage. |
| isManufacturable          | boolean                       | Whether this product can be manufactured. This is a read-only property that is calculated based on whether this product has a bill of materials defined. |
| itemBoms                  | Array of objects              |             |
| itemType                  | string                        | The type of this item. Cannot be changed once set. Stocked Product is most common.<br>Enum: "StockedProduct", "NonstockedProduct", "Service" |
| lastModifiedBy            | object (TeamMember)           |             |
| lastModifiedById          | string &lt;uuid&gt;                 | The inFlow Team Member, system process, or API key that last modified this product. This is set automatically, and cannot be set through the API. |
| lastModifiedDateTime      | string &lt;date-time&gt;            | Last modified datetime of this product. |
| lastVendor                | object (Vendor)               |             |
| lastVendorId              | string &lt;uuid&gt; Nullable        |             |
| length                    | string &lt;decimal&gt; Nullable     | Length of this product (unit depends on global setting) |
| name                      | string                        | An item name or title that should be human readable. Must be unique. |
| originCountry             | string                        | 2-character country code for country of origin. Used to pre-fill for customs forms when shipping. |
| prices                    | Array of objects              |             |
| productBarcodes           | Array of objects              |             |
| productId                 | string &lt;uuid&gt;                 | The primary identifier for this product. [When inserting, you should specify this by generating a GUID](/overview/#write-requests). Not shown to users |
| productOperations         | Array of objects              |             |
| purchasingUom             | object (UnitOfMeasure)        |             |
| remarks                   | string                        | A space for any extra remarks about this product. |
| reorderSettings           | Array of objects              |             |
| salesUom                  | object (UnitOfMeasure)        |             |
| sku                       | string                        | A SKU (stock-keeping-unit) code that represents a product in a concise, machine-friendly, way. Optional, but must be unique if specified. |
| standardUomName           | string                        | Standard unit of measure for tracking this product |
| taxCodes                  | Array of objects              |             |
| timestamp                 | string <rowversion>           | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |
| totalQuantityOnHand       | string &lt;decimal&gt;              | The total inventory quantity on hand across all locations for this product. Note that the InventoryLines relationship must be included for this attribute to be populated. |
| trackSerials              | boolean                       | Whether this product requires serial numbers. Cannot be changed once set. Only possible for Stocked Products. |
| vendorItems               | Array of objects              | A list of vendors that sell this item to you |
| weight                    | string &lt;decimal&gt; Nullable     | Weight of this product (unit depends on global setting) |
| width                     | string &lt;decimal&gt; Nullable     | Width of this product (unit depends on global setting) |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 1229 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-insert-or-update-product.json](response-sample-of-insert-or-update-product.json)
