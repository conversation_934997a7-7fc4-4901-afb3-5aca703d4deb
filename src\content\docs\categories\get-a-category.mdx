---
title: Get a category
description: Relationships can be included via the `include` query parameter.
---

Relationships can be included via the `include` query parameter.

### Request endpoint

```http
GET /{companyId}/categories/{categoryId}
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter  | Type          | Required | Description                   |
|------------|---------------|----------|-------------------------------|
| companyId  | string <uuid> | yes      | Your inFlow account companyId |
| categoryId | string <uuid> | yes      | Id of the Category to retrieve |

### Response

#### Success response (200) schema: `application/json`

| Field            | Type                     | Description                     |
|------------------|--------------------------|---------------------------------|
| categoryId       | string (uuid)            | The primary identifier for this category. Not shown to users |
| isDefault        | boolean                  | Only one category, your company-wide default, should have `IsDefault = true`. |
| name             | string                   | A human-readable name for this category. |
| parentCategory   | object (Category)        | Recursive                       |
| parentCategoryId | string (uuid) (Nullable) |                                 |
| timestamp        | string (rowversion)      | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

#### Success response (200) example

##### Content type: `application/json`

```json
{
  "categoryId": "00000000-0000-0000-0000-000000000000",
  "isDefault": true,
  "name": "Bestsellers",
  "parentCategory": { },
  "parentCategoryId": "00000000-0000-0000-0000-000000000000",
  "timestamp": "0000000000310AB6"
}
```
