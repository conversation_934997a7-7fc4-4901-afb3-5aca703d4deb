---
title: Insert or update taxing scheme
description: Insert or update taxing scheme endpoint documentation.
---


Relationships can be included via the `include` query parameter.

### Request endpoint

```http
PUT /{companyId}/taxing-schemes
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | Yes      | Your inFlow account companyId |

### Request body

**Request body schema:** `application/json`

A taxing scheme to insert or update

| Field               | Type                | Description              |
|---------------------|---------------------|--------------------------|
| calculateTax2OnTax1 | boolean             | Whether a secondary tax should be compounded on top of the primary tax |
| defaultTaxCode      | object (TaxCode)    |                          |
| defaultTaxCodeId    | string <uuid>       |                          |
| isActive            | boolean             | Taxing schemes with `IsActive = false` are deactivated and hidden away for new usage. |
| isDefault           | boolean             | Only one taxing scheme, your company-wide default, should have `IsDefault = true`. |
| name                | string              | Human-readable name for this pricing scheme. Not shown to customers on invoices, etc. |
| tax1Name            | string              | A short name for display of the primary tax |
| tax1OnShipping      | boolean             | Whether the primary tax applies to shipping/freight costs |
| tax2Name            | string              | A short name for display of the secondary tax |
| tax2OnShipping      | boolean             | Whether the secondary tax applies to shipping/freight costs |
| taxCodes            | Array of objects    | A list of other potential tax codes (percentages) for this taxing scheme, e.g. for tax-exempt items |
| taxingSchemeId      | string <uuid>       | The primary identifier for this taxing scheme. [When inserting, you should specify this by generating a GUID](/overview/#write-requests). Not shown to users |
| timestamp           | string <rowversion> | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

### Payload example

```json
{
  "calculateTax2OnTax1": true,
  "defaultTaxCode": {
    "isActive": true,
    "name": "Taxable",
    "tax1Rate": "19.99",
    "tax2Rate": "19.99",
    "taxCodeId": "00000000-0000-0000-0000-000000000000",
    "taxingScheme": {},
    "taxingSchemeId": "00000000-0000-0000-0000-000000000000",
    "timestamp": "0000000000310AB6"
  },
  "defaultTaxCodeId": "00000000-0000-0000-0000-000000000000",
  "isActive": true,
  "isDefault": true,
  "name": "NYC sales tax",
  "tax1Name": "VAT",
  "tax1OnShipping": true,
  "tax2Name": "PST",
  "tax2OnShipping": true,
  "taxCodes": [
    {
      "isActive": true,
      "name": "Taxable",
      "tax1Rate": "19.99",
      "tax2Rate": "19.99",
      "taxCodeId": "00000000-0000-0000-0000-000000000000",
      "taxingScheme": {},
      "taxingSchemeId": "00000000-0000-0000-0000-000000000000",
      "timestamp": "0000000000310AB6"
    }
  ],
  "taxingSchemeId": "00000000-0000-0000-0000-000000000000",
  "timestamp": "0000000000310AB6"
}
```

### Response

#### Success response (200) schema: `application/json`

| Field               | Type                | Description              |
|---------------------|---------------------|--------------------------|
| calculateTax2OnTax1 | boolean             | Whether a secondary tax should be compounded on top of the primary tax |
| defaultTaxCode      | object (TaxCode)    |                          |
| defaultTaxCodeId    | string <uuid>       |                          |
| isActive            | boolean             | Taxing schemes with `IsActive = false` are deactivated and hidden away for new usage. |
| isDefault           | boolean             | Only one taxing scheme, your company-wide default, should have `IsDefault = true`. |
| name                | string              | Human-readable name for this pricing scheme. Not shown to customers on invoices, etc. |
| tax1Name            | string              | A short name for display of the primary tax |
| tax1OnShipping      | boolean             | Whether the primary tax applies to shipping/freight costs |
| tax2Name            | string              | A short name for display of the secondary tax |
| tax2OnShipping      | boolean             | Whether the secondary tax applies to shipping/freight costs |
| taxCodes            | Array of objects    | A list of other potential tax codes (percentages) for this taxing scheme, e.g. for tax-exempt items |
| taxingSchemeId      | string <uuid>       | The primary identifier for this taxing scheme. [When inserting, you should specify this by generating a GUID](/overview/#write-requests). Not shown to users |
| timestamp           | string <rowversion> | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

#### Success response (200) example

##### Content type: `application/json`

```json
{
  "calculateTax2OnTax1": true,
  "defaultTaxCode": {
    "isActive": true,
    "name": "Taxable",
    "tax1Rate": "19.99",
    "tax2Rate": "19.99",
    "taxCodeId": "00000000-0000-0000-0000-000000000000",
    "taxingScheme": {},
    "taxingSchemeId": "00000000-0000-0000-0000-000000000000",
    "timestamp": "0000000000310AB6"
  },
  "defaultTaxCodeId": "00000000-0000-0000-0000-000000000000",
  "isActive": true,
  "isDefault": true,
  "name": "NYC sales tax",
  "tax1Name": "VAT",
  "tax1OnShipping": true,
  "tax2Name": "PST",
  "tax2OnShipping": true,
  "taxCodes": [
    {
      "isActive": true,
      "name": "Taxable",
      "tax1Rate": "19.99",
      "tax2Rate": "19.99",
      "taxCodeId": "00000000-0000-0000-0000-000000000000",
      "taxingScheme": {},
      "taxingSchemeId": "00000000-0000-0000-0000-000000000000",
      "timestamp": "0000000000310AB6"
    }
  ],
  "taxingSchemeId": "00000000-0000-0000-0000-000000000000",
  "timestamp": "0000000000310AB6"
}
```
