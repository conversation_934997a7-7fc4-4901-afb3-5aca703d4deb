---
title: Get a stock adjustment
description: Get a stock adjustment endpoint documentation.
---


Relationships can be included via the `include` query parameter.

### Request endpoint

```http
GET /{companyId}/stock-adjustments/{stockAdjustmentId}
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter         | Type          | Required | Description                         |
|-------------------|---------------|----------|-------------------------------------|
| companyId         | string &lt;uuid&gt; | Yes      | Your inFlow account companyId       |
| stockAdjustmentId | string &lt;uuid&gt; | Yes      | The stockAdjustmentId to be fetched |

### Response

#### Success response (200) schema: `application/json`

| Field              | Type                | Description      |
|--------------------|---------------------|------------------|	
| adjustmentNumber   | string              | An identifier for this stock adjustment and shown on printed documents. |
| adjustmentReason   | object (AdjustmentReason) |            |
| adjustmentReasonId | string &lt;uuid&gt; (Nullable) |             |
| customFields       | object (LargeCustomFieldValues) |      |
| date               | string <date-time>  | The effective date of this stock adjustment. |
| isCancelled        | boolean             | Whether this adjustment is cancelled (being cancelled voids inventory movements) |
| lastModifiedBy     | object (TeamMember) |                  |
| lastModifiedById   | string &lt;uuid&gt;      | The inFlow Team Member, system process, or API key that last modified this stock adjustment. This is set automatically, and cannot be set through the API. |
| lines              | Array of objects    | Lines representing which inventory levels are being adjusted |
| location           | object (Location)   |                  |
| locationId         | string &lt;uuid&gt;       |                  |
| remarks            | string              | Any extra comments on this stock adjustment |
| stockAdjustmentId  | string &lt;uuid&gt;       | The primary identifier for this stock adjustment. [When inserting, you should specify this by generating a GUID](/overview/#write-requests). Not shown to users |
| timestamp          | string <rowversion> | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 2537 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-get-a-stock-adjustment.json](response-sample-of-get-a-stock-adjustment.json)
