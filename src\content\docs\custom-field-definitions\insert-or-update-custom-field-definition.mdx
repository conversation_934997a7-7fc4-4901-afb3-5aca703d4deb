---
title: Insert or update custom field definition
description: Insert or update custom field definition endpoint documentation.
---

### Request endpoint

```http
PUT /{companyId}/custom-field-definitions
```

Base URL: https://cloudapi.inflowinventory.com

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string <uuid> | yes      | Your inFlow account companyId |

### Request body

**Request body schema:** `application/json`

A custom field definition to insert or update

| Field               | Type              | Description                |
|---------------------|-------------------|----------------------------|
| category            | `Category` object |                            |
| categoryId          | string <uuid> Nullable |                       |
| customFieldDefinitionId | string        | The primary identifier for this custom field definition. [When inserting, you can insert any value here, it will be ignored](/overview/#write-requests). Not shown to users |
| customFieldType     | string            | The type of custom field and how it will be displayed to the user |
| entityType          | string            | The entity type of the custom field, e.g. `product` |
| isActive            | boolean           | Whether this custom field is active and should be displayed |
| label               | string            | Human-readable name for this custom field. |
| propertyName        | string            | The property name for the custom field, should be one of custom1 through custom10 |

Available enum string values for `customFieldType`:
- "Text"
- "Dropdown"
- "Date"
- "Checkbox"

Available enum string values for `entityType`:
- "SalesOrder"
- "PurchaseOrder"
- "Product"
- "Vendor"
- "Customer"
- "CountSheet"
- "StockTransfer"
- "StockAdjustment"
- "StockCount"
- "ManufacturingOrder"

### Payload example

```json
{
  "category": {
    "categoryId": "00000000-0000-0000-0000-000000000000",
    "isDefault": true,
    "name": "Bestsellers",
    "parentCategory": {},
    "parentCategoryId": "00000000-0000-0000-0000-000000000000",
    "timestamp": "0000000000310AB6"
  },
  "categoryId": "00000000-0000-0000-0000-000000000000",
  "customFieldDefinitionId": "string",
  "customFieldType": "Text",
  "entityType": "SalesOrder",
  "isActive": true,
  "label": "string",
  "propertyName": "string"
}
```

### Response

#### Success response (200) schema: `application/json`

| Field           | Type                   | Description               |
|-----------------|------------------------|---------------------------|
| category        | `Category` object      |                           |
| categoryId      | string <uuid> Nullable |                           |
| customFieldDefinitionId | string         | The primary identifier for this custom field definition. [When inserting, you can insert any value here, it will be ignored](/overview/#write-requests). Not shown to users |
| customFieldType | string of enum         | The type of custom field and how it will be displayed to the user |
| entityType      | string of enum         | The entity type of the custom field, e.g. `product` |
| isActive        | boolean                | Whether this custom field is active and should be displayed |
| label           | string                 | Human-readable name for this custom field. |
| propertyName    | string                 | The property name for the custom field, should be one of custom1 through custom10 |

Available enum string values for `customFieldType`:
- "Text"
- "Dropdown"
- "Date"
- "Checkbox"

Available enum string values for `entityType`:
- "SalesOrder"
- "PurchaseOrder"
- "Product"
- "Vendor"
- "Customer"
- "CountSheet"
- "StockTransfer"
- "StockAdjustment"
- "StockCount"
- "ManufacturingOrder"

#### Success response (200) example

##### Content type: `application/json`

```json
{
  "category": {
    "categoryId": "00000000-0000-0000-0000-000000000000",
    "isDefault": true,
    "name": "Bestsellers",
    "parentCategory": {},
    "parentCategoryId": "00000000-0000-0000-0000-000000000000",
    "timestamp": "0000000000310AB6"
  },
  "categoryId": "00000000-0000-0000-0000-000000000000",
  "customFieldDefinitionId": "string",
  "customFieldType": "Text",
  "entityType": "SalesOrder",
  "isActive": true,
  "label": "string",
  "propertyName": "string"
}
```
