---
title: Inflow Inventory API Endpoints
description: List of all available endpoints for the inFlow API
---

import { Tabs, TabItem } from '@astrojs/starlight/components';
import { Code } from '@astrojs/starlight/components';

## Adjustment Reason

### Get adjustment reasons

<Code lang='http' title='GET' code={`/{companyId}/adjustment-reasons/{adjustmentReasonId}`} />

### List adjustment reasons
<Code lang='http' title='GET' code={`/{companyId}/adjustment-reasons`} />

## Categories

### Get a category
<Code lang='http' title='GET' code={`/{companyId}/categories/{categoryId}`} />

### List categories
<Code lang='http' title='GET' code={`/{companyId}/categories`} />

## Currency

### List currencies
<Code lang='http' title='GET' code={`/{companyId}/currencies`} />

### Get a currency
<Code lang='http' title='GET' code={`/{companyId}/currencies/{currencyId}`} />

## Customer

### Get a customer
<Code lang='http' title='GET' code={`/{companyId}/customers/{customerId}`} />

### List customers
<Code lang='http' title='GET' code={`/{companyId}/customers`} />

### Insert or update a customer
<Code lang='http' title='PUT' code={`/{companyId}/customers`} />

## Custom Field Definitions

### Get custom field definitions
<Code lang='http' title='GET' code={`/{companyId}/custom-field-definitions`} />

### Insert or update custom field definition
<Code lang='http' title='PUT' code={`/{companyId}/custom-field-definitions`} />

### Get all dropdown custom field options for an entity
<Code lang='http' title='GET' code={`/{companyId}/custom-field-dropdown-options/{entityType}`} />

### Set custom field dropdown options
<Code lang='http' title='PUT' code={`/{companyId}/custom-field-dropdown-options`} />

## Custom Fields

### Get custom field labels
<Code lang='http' title='GET' code={`/{companyId}/custom-fields`} />

### Insert or Update custom field labels
<Code lang='http' title='PUT' code={`/{companyId}/custom-fields`} />

## Location

### Get a location
<Code lang='http' title='GET' code={`/{companyId}/locations/{locationId}`} />

### List locations
<Code lang='http' title='GET' code={`/{companyId}/locations`} />

### Get suggested sublocations
<Code lang='http' title='GET' code={`/{companyId}/locations/{locationId}/suggested-sublocations`} />

### Get suggested sublocations for a given product and location
<Code lang='http' title='GET' code={`/{companyId}/locations/{locationId}/products/{productId}/suggested-sublocations`} />

## Manufacturing Order

### Get a manufacture order
<Code lang='http' title='GET' code={`/{companyId}/manufacturing-orders/{manufacturingOrderId}`} />

### Insert or update a manufacture order
<Code lang='http' title='PUT' code={`/{companyId}/manufacturing-orders`} />

### List manufacture orders
<Code lang='http' title='GET' code={`/{companyId}/manufacturing-orders`} />

### Get manufacturing order operation status
<Code lang='http' title='GET' code={`/{companyId}/manufacturing-orders/{manufacturingOrderId}/operation-status/{operationId}`} />

### Insert or update manufacturing order operation status
<Code lang='http' title='PUT' code={`/{companyId}/manufacturing-orders/{manufacturingOrderGuid}/operation-statuses`} />

## Operation Type

### Get an Operation Type
<Code lang='http' title='GET' code={`/{companyId}/operation-types/{operationTypeId}`} />

### List Operation Types
<Code lang='http' title='GET' code={`/{companyId}/operation-types`} />

## Payment Terms

### Get payment terms
<Code lang='http' title='GET' code={`/{companyId}/payment-terms/{paymentTermsId}`} />

### List payment terms
<Code lang='http' title='GET' code={`/{companyId}/payment-terms`} />

## Pricing Scheme

### Get a pricing scheme
<Code lang='http' title='GET' code={`/{companyId}/pricing-schemes/{pricingSchemeId}`} />

### List pricing schemes
<Code lang='http' title='GET' code={`/{companyId}/pricing-schemes`} />

## Product

### Get a product
<Code lang='http' title='GET' code={`/{companyId}/products/{productId}`} />

### List products
<Code lang='http' title='GET' code={`/{companyId}/products`} />

### Insert or update product
<Code lang='http' title='PUT' code={`/{companyId}/products`} />

### Get product inventory summary
<Code lang='http' title='GET' code={`/{companyId}/products/{productId}/inventory-summary`} />

### Get multiple product inventory summaries
<Code lang='http' title='POST' code={`/{companyId}/products/summary`} />

## Product Cost Adjustment

### Get a product cost adjustment
<Code lang='http' title='GET' code={`/{companyId}/product-cost-adjustments/{productCostAdjustmentId}`} />

### List product cost adjustments
<Code lang='http' title='GET' code={`/{companyId}/product-cost-adjustments`} />

### Insert or update product cost adjustment
<Code lang='http' title='PUT' code={`/{companyId}/product-cost-adjustments`} />

## Purchase Order

### List purchase orders
<Code lang='http' title='GET' code={`/{companyId}/purchase-orders`} />

### Insert or update purchase order
<Code lang='http' title='PUT' code={`/{companyId}/purchase-orders`} />

### Get a purchase order
<Code lang='http' title='GET' code={`/{companyId}/purchase-orders/{purchaseOrderId}`} />

## Sales Order

### Get a sales order
<Code lang='http' title='GET' code={`/{companyId}/sales-orders/{salesOrderId}`} />

### List sales orders
<Code lang='http' title='GET' code={`/{companyId}/sales-orders`} />

### Insert or update sales order
<Code lang='http' title='PUT' code={`/{companyId}/sales-orders`} />

## Stock Adjustment

### Get a stock adjustment
<Code lang='http' title='GET' code={`/{companyId}/stock-adjustments/{stockAdjustmentId}`} />

### List stock adjustments
<Code lang='http' title='GET' code={`/{companyId}/stock-adjustments`} />

### Insert or update a stock adjustment
<Code lang='http' title='PUT' code={`/{companyId}/stock-adjustments`} />

## Stock Count

### Get a stock count
<Code lang='http' title='GET' code={`/{companyId}/stock-counts/{stockCountId}`} />

### List stock counts
<Code lang='http' title='GET' code={`/{companyId}/stock-counts`} />

### Insert or update a stock count
<Code lang='http' title='PUT' code={`/{companyId}/stock-counts`} />

### Delete a count sheet
<Code lang='http' title='DELETE' code={`/{companyId}/stock-counts/{stockCountId}/count-sheets/{countSheetId}`} />

### Get a count sheet
<Code lang='http' title='GET' code={`/{companyId}/count-sheets/{countSheetId}`} />

### Insert or update a count sheet
<Code lang='http' title='PUT' code={`/{companyId}/count-sheets`} />

## Stockroom Scan

### List stockroom scans
<Code lang='http' title='GET' code={`/{companyId}/stockroom-scans`} />

### Insert or update a stockroom scan
<Code lang='http' title='PUT' code={`/{companyId}/stockroom-scans`} />

## Stockroom User

### Get stockroom users
<Code lang='http' title='GET' code={`/{companyId}/stockroom-users/{stockroomUserId}`} />

### List stockroom users
<Code lang='http' title='GET' code={`/{companyId}/stockroom-users`} />

## Stock Transfer

### Get a stock transfer
<Code lang='http' title='GET' code={`/{companyId}/stock-transfers/{stockTransferId}`} />

### List stock transfers
<Code lang='http' title='GET' code={`/{companyId}/stock-transfers`} />

### Insert or update a stock transfer
<Code lang='http' title='PUT' code={`/{companyId}/stock-transfers`} />

## Tax Code

### Get a tax code
<Code lang='http' title='GET' code={`/{companyId}/tax-codes/{taxCodeId}`} />

### List tax codes
<Code lang='http' title='GET' code={`/{companyId}/tax-codes`} />

## Taxing Scheme

### Get a taxing scheme
<Code lang='http' title='GET' code={`/{companyId}/taxing-schemes/{taxingSchemeId}`} />

### List taxing schemes
<Code lang='http' title='GET' code={`/{companyId}/taxing-schemes`} />

### Insert or update taxing scheme
<Code lang='http' title='PUT' code={`/{companyId}/taxing-schemes`} />

## Team Member

### List team members
<Code lang='http' title='GET' code={`/{companyId}/team-members`} />

## Vendor

### Get a vendor
<Code lang='http' title='GET' code={`/{companyId}/vendors/{vendorId}`} />

### List vendors
<Code lang='http' title='GET' code={`/{companyId}/vendors`} />

### Insert or update a vendor
<Code lang='http' title='PUT' code={`/{companyId}/vendors`} />

## Web Hooks

### List all subscribed webhooks
<Code lang='http' title='GET' code={`/{companyId}/webhooks`} />

### Subscribe to a webhook
<Code lang='http' title='PUT' code={`/{companyId}/webhooks`} />

### Get a webhook subscription
<Code lang='http' title='GET' code={`/{companyId}/webhooks/{webHookId}`} />

### Unsubscribe from a webhook
<Code lang='http' title='DELETE' code={`/{companyId}/webhooks/{webHookId}`} />

