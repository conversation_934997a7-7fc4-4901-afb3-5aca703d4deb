---
title: List stock transfers
description: List stock transfers endpoint documentation.
---


Relationships can be included via the `include` query parameter.  

Options for filtering this list:  
- `filter[transferNumber]`  

### Request endpoint

```http
GET /{companyId}/stock-transfers
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string &lt;uuid&gt; | Yes      | Your inFlow account companyId |

### Query parameters

| Parameter            | Type              | Description               |
|----------------------|-------------------|---------------------------|
| request              | object (GetCollectionRequest) | Additional query parameter options |
| request.includeCount | boolean           | Return count in X-listCount header |
| request.count        | integer (int32)   | Max 100 per request       |
| request.after        | string (Nullable) | Entity ID for pagination  |
| request.before       | string (Nullable) | Entity ID for pagination  |
| request.start        | string (Nullable) | Include this entity and everything after |
| request.skip         | integer (int32)   | Number of records to skip |
| request.sort         | string (Nullable) | Property name to sort by  |
| request.sortDesc     | boolean           | If true, sort descending  |

### Response

#### Success response (200) schema: `application/json`

Array of StockTransfer objects. Each StockTransfer object has the following properties:

| Field                  | Type                | Description           |
|------------------------|---------------------|-----------------------|
| assignedToTeamMember   | object (TeamMember) |                       |
| assignedToTeamMemberId | string &lt;uuid&gt; (Nullable) |                  |
| customFields           | object (LargeCustomFieldValues) |           |
| fromLocation           | object (Location)   |                       |
| fromLocationId         | string &lt;uuid&gt;       |                       |
| isCancelled            | boolean             | Whether this adjustment is cancelled (being cancelled voids inventory movements) |
| lastModifiedBy         | object (TeamMember) |                       |
| lastModifiedById       | string &lt;uuid&gt;       | The inFlow Team Member, system process, or API key that last modified this stock transfer. This is set automatically, and cannot be set through the API. |
| lines                  | Array of objects    | Lines representing which the inventory movements |
| receivedDate           | string <date-time> (Nullable) | The date stock was added to the destination location. |
| remarks                | string              | Any extra comments on this stock transfer |
| sentDate               | string <date-time> (Nullable) | The date stock was removed from the source location. |
| status                 | string              | The status of the stock transfer (this determines when stock is removed and added)<br>Enum: `"Open"`, `"InTransit"`, `"Completed"` |
| stockTransferId        | string &lt;uuid&gt;       | The primary identifier for this stock transfer. . [When inserting, you should specify this by generating a GUID](/overview/#write-requests). Not shown to users |
| timestamp              | string <rowversion> | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |
| toLocation             | object (Location)   |                       |
| toLocationId           | string &lt;uuid&gt;       |                       |
| transferDate           | string <date-time>  | The date this stock transfer was requested. |
| transferNumber         | string              | An identifier for this stock transfer and shown on printed documents. |

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 2570 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-list-stock-transfers.json](response-sample-of-list-stock-transfers.json)
