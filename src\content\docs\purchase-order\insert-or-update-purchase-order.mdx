---
title: Insert or update purchase order
description: Insert or update purchase order endpoint documentation.
---


### Request endpoint

```http
PUT /{companyId}/purchase-orders
```

Base URL: `https://cloudapi.inflowinventory.com`

### Path parameters

| Parameter | Type          | Required | Description                   |
|-----------|---------------|----------|-------------------------------|
| companyId | string &lt;uuid&gt; | Yes      | Your inFlow account companyId |

### Request body

**Request body schema:** `application/json`

A purchase order to insert or update.

**Note**:
- `purchaseOrderId` property is required, please generate a GUID when inserting.  
- `vendorId` property is required, which should be the primary identifier for a vendor.  
- Many of the properties, e.g. Subtotal, and tax amounts, are optional and will be calculated by inFlow if you exclude the properties.

| Field                  | Type                     | Description      |
|------------------------|--------------------------|------------------|
| amountPaid             | string &lt;decimal&gt;         | The amount that you have paid this vendor. |
| approverTeamMember     | object (TeamMember)      |                  |
| approverTeamMemberId   | string &lt;uuid&gt; (Nullable) |                  |
| assignedToTeamMember   | object (TeamMember)      |                  |
| assignedToTeamMemberId | string &lt;uuid&gt; (Nullable) |                  |
| balance                | string &lt;decimal&gt;         | The remaining amount that you owe this vendor. |
| calculateTax2OnTax1    | boolean                  | Whether a secondary tax should be compounded on top of the primary tax |
| carrier                | string                   | The carrier or shipping method for this order |
| contactName            | string                   | The name of the vendor's employee that you should contact for this order |
| currency               | object (Currency)        |                  |
| currencyId             | string &lt;uuid&gt;            |                  |
| customFields           | object (LargeCustomFieldValues) |           |
| dueDate                | string &lt;date-time&gt; (Nullable) | The date by which payment is due |
| email                  | string                   | The email address for the vendor that you should contact for this order |
| exchangeRate           | string &lt;decimal&gt;         | The exchange rate between the currency in this order and your home currency effective for this order |
| exchangeRateAutoPulled | string &lt;date-time&gt; (Nullable) | If this exchange rate was automatically pulled, then the date it was set, otherwise null. |
| freight                | string &lt;decimal&gt; (Nullable) | The amount this vendor charges you for shipping |
| inventoryStatus        | string                   | The inventory-related status of this order (Enum: "Quote", "Unapproved", "Unfulfilled", "Started", "Fulfilled") |
| isCancelled            | boolean                  | Whether this order is cancelled (being cancelled voids any payments and inventory movements) |
| isCompleted            | boolean                  | Whether this order is completed (fully received) |
| isQuote                | boolean                  | Whether this order is a quote |
| isTaxInclusive         | boolean                  | When `true`, then prices should be treated as tax-inclusive. |
| lastModifiedBy         | object (TeamMember)      |
| lastModifiedById       | string &lt;uuid&gt;            | The inFlow Team Member, system process, or API key that last modified this purchase order. This is set automatically, and cannot be set through the API. |
| lines                  | Array of objects         | Lines representing which goods have been ordered and returned |
| location               | object (Location)        |                  |
| locationId             | string &lt;uuid&gt; (Nullable) |                  |
| nonVendorCosts         | object (PercentOrFixedAmount) |             |
| orderDate              | string &lt;date-time&gt;       | The date this order was placed. |
| orderNumber            | string                   | An identifier for this purchase order and shown on printed documents. |
| orderRemarks           | string                   | Any extra comments on this order |
| paymentLines           | Array of objects         | Lines representing a history of payment details for this order. |
| paymentStatus          | string                   | The total amount the customer should pay, including taxes and shipping (Enum: "Quote", "Unapproved", "Unpaid", "Partial", "Paid", "Owing") |
| paymentTerms           | object (PaymentTerms)    |                  |
| paymentTermsId         | string &lt;uuid&gt; (Nullable) |                  |
| phone                  | string                   | The phone number for the vendor that you should contact for this order |
| purchaseOrderId        | string &lt;uuid&gt;            | The primary identifier for this purchase order. [When inserting, you should specify this by generating a GUID](/overview/#write-requests). Not shown to users |
| receiveLines           | Array of objects         | Lines representing which goods have been received into your warehouse |
| receiveRemarks         | string                   | Any extra comments on this order regarding receiving |
| requestShipDate        | string &lt;date-time&gt; (Nullable) | The date that you request that this order be shipped |
| returnExtra            | string &lt;decimal&gt;         | The amount that this vendor refunds you for returns related to shipping |
| returnFee              | string &lt;decimal&gt;         | The amount this vendor charges you for return fees |
| returnRemarks          | string                   | Any extra comments on this order regarding returns |
| shipToAddress          | object (Address)         |                  |
| shipToCompanyName      | string                   | The ship-to company name shown on printed documents, e.g. for dropshipping |
| showShipping           | boolean                  | Whether this order will be shipped; this controls whether certain fields will be shown. |
| subTotal               | string &lt;decimal&gt;         | The total of line items for this order |
| tax1                   | string &lt;decimal&gt;         | The calculated primary tax amount for this order |
| tax1Name               | string                   | A short name for display of the primary tax |
| tax1OnShipping         | boolean                  | Whether the primary tax applies to shipping/freight costs |
| tax1Rate               | string &lt;decimal&gt; (Nullable) | The default percentage primary tax for this order. |
| tax2                   | string &lt;decimal&gt;         | The calculated secondary tax amount for this orders |
| tax2Name               | string                   | A short name for display of the secondary tax |
| tax2OnShipping         | boolean                  | Whether the secondary tax applies to shipping/freight costs |
| tax2Rate               | string &lt;decimal&gt; (Nullable) | The default percentage secondary tax for this order. |
| taxingScheme           | object (TaxingScheme)    |                  |
| taxingSchemeId         | string &lt;uuid&gt;            |                  |
| timestamp              | string <rowversion>      | You can optionally include the last-known timestamp when modifying to protect against concurrent modifications. |
| total                  | string &lt;decimal&gt;         | The total amount you should pay, including taxes and shipping |
| unstockLines           | Array of objects         | Lines representing which returned items have been unstocked |
| unstockRemarks         | string                   | Any extra comments on this order regarding unstocking |
| vendor                 | object (Vendor)          |                  |
| vendorAddress          | object (Address)         |                  |
| vendorId               | string &lt;uuid&gt;            |                  |
| vendorOrderNumber      | string                   | The vendor's number for this order. |

### Payload example

> **WARNING**: The payload sample is 7224 lines long.
> Please follow the link below to see payload JSON.

[payload-sample-of-insert-or-update-a-purchase-order.json](payload-sample-of-insert-or-update-a-purchase-order.json)

### Response

#### Success response (200) schema: `application/json`

Same fields as Request Body schema above.

#### Success response (200) example

##### Content type: `application/json`

> **WARNING**: The response sample is 7224 lines long.
> Please follow the link below to see response JSON.

[response-sample-of-insert-or-update-a-purchase-order.json](response-sample-of-insert-or-update-a-purchase-order.json)
